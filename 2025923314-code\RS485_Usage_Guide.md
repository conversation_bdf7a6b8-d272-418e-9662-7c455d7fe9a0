# GD32F470VE UART1转RS485使用指南

## 硬件连接

### RS485接口引脚定义
- **PA2**: UART1_TX (RS485 A+/B-)
- **PA3**: UART1_RX (RS485 A+/B-)  
- **PA4**: DE/RE控制引脚 (方向控制)

### 连接说明
1. 将PA2连接到RS485收发器的DI引脚
2. 将PA3连接到RS485收发器的RO引脚
3. 将PA4连接到RS485收发器的DE和RE引脚（并联）
4. RS485收发器的A+和B-连接到RS485总线

## 软件配置

### 初始化
系统启动时会自动调用 `bsp_rs485_init()` 初始化RS485功能：
- 波特率：115200
- 数据位：8位
- 停止位：1位
- 校验位：无
- 流控：无

### 主要函数

#### 1. 发送数据
```c
void rs485_send_data(uint8_t *data, uint16_t length);
```
- 自动控制DE/RE引脚切换到发送模式
- 发送完成后自动切换回接收模式

#### 2. 接收处理
```c
void rs485_Proc(void);
```
- 在调度器中每5ms调用一次
- 处理接收到的RS485数据
- 当前实现为回显功能

#### 3. 测试函数
```c
void rs485_test(void);
```
- 发送测试消息 "RS485 Test Message"

## 使用方法

### 通过UART0命令控制RS485

1. **发送测试消息**
   ```
   rs485 test
   ```

2. **发送自定义消息**
   ```
   rs485 send Hello World
   ```

### 编程接口

#### 发送数据示例
```c
char message[] = "Hello RS485!";
rs485_send_data((uint8_t*)message, strlen(message));
```

#### 接收数据处理
接收到的数据会触发USART1中断，数据存储在 `rs485_uart_dma_buffer` 中，
`rs485_rx_flag` 标志位会被设置为1。

## 技术细节

### DMA配置
- 使用DMA0 Channel5进行UART1接收
- 缓冲区大小：512字节
- 支持IDLE中断检测数据帧结束

### 方向控制
- DE/RE引脚控制RS485收发器的工作模式
- 发送时：DE/RE = 1 (高电平)
- 接收时：DE/RE = 0 (低电平)
- 自动切换，无需手动控制

### 中断处理
- USART1_IRQHandler 处理接收中断
- 使用IDLE中断检测数据帧结束
- 自动重新配置DMA接收

## 注意事项

1. **时序控制**：发送前后都有延时确保DE/RE信号稳定
2. **总线冲突**：确保同一时间只有一个设备发送数据
3. **终端电阻**：RS485总线两端需要添加120Ω终端电阻
4. **地线连接**：确保所有设备共地
5. **电源隔离**：建议使用隔离型RS485收发器

## 调试建议

1. 使用示波器检查DE/RE信号切换时序
2. 检查RS485差分信号质量
3. 确认总线终端电阻正确连接
4. 使用USB转RS485适配器与PC通信测试

## 扩展功能

可以根据需要扩展以下功能：
- Modbus RTU协议支持
- 自定义通信协议
- 多设备地址管理
- 错误检测和重传机制
