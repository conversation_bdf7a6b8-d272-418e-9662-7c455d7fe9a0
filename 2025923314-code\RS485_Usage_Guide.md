# GD32F470VE UART1转RS485使用指南 (MAX3485ESA)

## 硬件连接

### MAX3485ESA连接图
```
GD32F470VE          MAX3485ESA          USB转RS485
PA2(UART1_TX) ----> DI (Pin 4)
PA3(UART1_RX) <---- RO (Pin 1)
PA1(GPIO)     ----> DE (Pin 2)
PA1(GPIO)     ----> RE (Pin 3)
GND           ----> GND (Pin 5)
VCC(3.3V)     ----> VCC (Pin 8)
                    A+ (Pin 6)  -----> A+
                    B- (Pin 7)  -----> B-
                                        GND -----> GND
```

### 引脚定义
- **PA2**: UART1_TX → MAX3485ESA DI引脚
- **PA3**: UART1_RX ← MAX3485ESA RO引脚
- **PA1**: DE/RE控制引脚 → MAX3485ESA DE和RE引脚(并联)

### 连接说明
1. PA2连接到MAX3485ESA的DI引脚(Pin 4) - 数据输入
2. PA3连接到MAX3485ESA的RO引脚(Pin 1) - 接收输出
3. PA1连接到MAX3485ESA的DE和RE引脚(Pin 2和Pin 3并联) - 方向控制
4. MAX3485ESA的A+(Pin 6)和B-(Pin 7)连接到USB转RS485的A+和B-
5. 确保所有设备共地连接

## 软件配置

### 初始化
系统启动时会自动调用 `bsp_rs485_init()` 初始化RS485功能：
- 波特率：115200
- 数据位：8位
- 停止位：1位
- 校验位：无
- 流控：无

### 主要函数

#### 1. 发送数据
```c
void rs485_send_data(uint8_t *data, uint16_t length);
```
- 自动控制DE/RE引脚切换到发送模式
- 发送完成后自动切换回接收模式

#### 2. 接收处理
```c
void rs485_Proc(void);
```
- 在调度器中每5ms调用一次
- 处理接收到的RS485数据
- 当前实现为回显功能

#### 3. 测试函数
```c
void rs485_test(void);
```
- 发送测试消息 "RS485 Test Message"

## 使用方法

### 通过UART0命令控制RS485

1. **发送测试消息**
   ```
   rs485 test
   ```

2. **发送十六进制测试数据**
   ```
   rs485 hex
   ```

3. **发送自定义消息**
   ```
   rs485 send Hello World
   ```

### 编程接口

#### 发送数据示例
```c
char message[] = "Hello RS485!";
rs485_send_data((uint8_t*)message, strlen(message));
```

#### 接收数据处理
接收到的数据会触发USART1中断，数据存储在 `rs485_uart_dma_buffer` 中，
`rs485_rx_flag` 标志位会被设置为1。

## 技术细节

### DMA配置
- 使用DMA0 Channel5进行UART1接收
- 缓冲区大小：512字节
- 支持IDLE中断检测数据帧结束

### 方向控制
- DE/RE引脚控制RS485收发器的工作模式
- 发送时：DE/RE = 1 (高电平)
- 接收时：DE/RE = 0 (低电平)
- 自动切换，无需手动控制

### 中断处理
- USART1_IRQHandler 处理接收中断
- 使用IDLE中断检测数据帧结束
- 自动重新配置DMA接收

## 注意事项

1. **MAX3485ESA时序**：发送前后都有2ms延时确保DE/RE信号稳定
2. **电源电压**：MAX3485ESA工作电压为3.3V，与GD32F470VE兼容
3. **总线冲突**：确保同一时间只有一个设备发送数据
4. **终端电阻**：RS485总线两端需要添加120Ω终端电阻
5. **地线连接**：确保GD32、MAX3485ESA、USB转RS485共地
6. **信号完整性**：使用双绞线连接A+和B-，减少干扰
7. **DE/RE控制**：PA1同时控制MAX3485ESA的DE和RE引脚

## 调试建议

1. 使用示波器检查DE/RE信号切换时序
2. 检查RS485差分信号质量
3. 确认总线终端电阻正确连接
4. 使用USB转RS485适配器与PC通信测试

## 扩展功能

可以根据需要扩展以下功能：
- Modbus RTU协议支持
- 自定义通信协议
- 多设备地址管理
- 错误检测和重传机制
