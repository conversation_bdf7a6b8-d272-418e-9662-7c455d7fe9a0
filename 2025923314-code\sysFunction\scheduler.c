#include "CMIC_GD32f470vet6.h"
uint8_t scheduled_jobs_count;

typedef struct {
    void (*job_handler)(void);
    uint32_t execution_interval;
    uint32_t last_execution_time;
} job_entry;

// �������ñ�
static job_entry scheduled_jobs[] =
{
     {Cai<PERSON>,  100,   0}  // ��������
    ,{oled_Proc,     100,    0}  // OLED��ʾ����
    ,{btn_Proc,        5,    0}  // ��ť����
    ,{uart_Proc,       5,    0}  // ���ڴ���
    ,{rs485_Proc,      5,    0}  // RS485����
    ,{rtc_Proc,      500,    0}  // RTC����
};


void scheduler_init(void) //��ʼ��
{
    scheduled_jobs_count = sizeof(scheduled_jobs) / sizeof(scheduled_jobs[0]);
}

void scheduler_run(void)  //����
{
    uint32_t current_timestamp = get_system_ms();
    
    for (uint8_t i = 0; i < scheduled_jobs_count; i++)
    {
        job_entry* current_job = &scheduled_jobs[i];
        
        // ��������Ƿ񵽴�ִ��ʱ��
        if ((current_timestamp - current_job->last_execution_time) >= current_job->execution_interval)
        {
            current_job->last_execution_time = current_timestamp;
            current_job->job_handler();
            
            // ִ�к��������أ�ȷ������ѭ��ִֻ��һ������
            break;
        }
    }
}

